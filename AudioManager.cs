using Microsoft.Xna.Framework.Audio;
using Microsoft.Xna.Framework.Content;
using System;
using System.Collections.Generic;

namespace SubwaySurfers2D
{
    public class AudioManager
    {
        private Dictionary<string, SoundEffect> _soundEffects;
        private Dictionary<string, SoundEffectInstance> _soundInstances;
        private bool _soundEnabled;
        
        public bool SoundEnabled 
        { 
            get => _soundEnabled; 
            set => _soundEnabled = value; 
        }
        
        public AudioManager()
        {
            _soundEffects = new Dictionary<string, SoundEffect>();
            _soundInstances = new Dictionary<string, SoundEffectInstance>();
            _soundEnabled = true;
        }
        
        public void LoadContent(ContentManager content)
        {
            try
            {
                // Load sound effects (these would be actual audio files in a real project)
                // For now, we'll create placeholder entries
                
                // _soundEffects["jump"] = content.Load<SoundEffect>("Sounds/jump");
                // _soundEffects["coin"] = content.Load<SoundEffect>("Sounds/coin");
                // _soundEffects["crash"] = content.Load<SoundEffect>("Sounds/crash");
                // _soundEffects["background"] = content.Load<SoundEffect>("Sounds/background");
                
                // Create instances for looping sounds
                // if (_soundEffects.ContainsKey("background"))
                // {
                //     _soundInstances["background"] = _soundEffects["background"].CreateInstance();
                //     _soundInstances["background"].IsLooped = true;
                //     _soundInstances["background"].Volume = 0.3f;
                // }
            }
            catch (Exception ex)
            {
                // Handle missing audio files gracefully
                Console.WriteLine($"Audio loading failed: {ex.Message}");
            }
        }
        
        public void PlaySound(string soundName, float volume = 1.0f, float pitch = 0.0f, float pan = 0.0f)
        {
            if (!_soundEnabled || !_soundEffects.ContainsKey(soundName))
                return;
                
            try
            {
                _soundEffects[soundName].Play(volume, pitch, pan);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to play sound {soundName}: {ex.Message}");
            }
        }
        
        public void PlaySoundInstance(string instanceName)
        {
            if (!_soundEnabled || !_soundInstances.ContainsKey(instanceName))
                return;
                
            try
            {
                var instance = _soundInstances[instanceName];
                if (instance.State != SoundState.Playing)
                {
                    instance.Play();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to play sound instance {instanceName}: {ex.Message}");
            }
        }
        
        public void StopSoundInstance(string instanceName)
        {
            if (!_soundInstances.ContainsKey(instanceName))
                return;
                
            try
            {
                _soundInstances[instanceName].Stop();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to stop sound instance {instanceName}: {ex.Message}");
            }
        }
        
        public void PauseAllSounds()
        {
            foreach (var instance in _soundInstances.Values)
            {
                if (instance.State == SoundState.Playing)
                {
                    instance.Pause();
                }
            }
        }
        
        public void ResumeAllSounds()
        {
            foreach (var instance in _soundInstances.Values)
            {
                if (instance.State == SoundState.Paused)
                {
                    instance.Resume();
                }
            }
        }
        
        public void StopAllSounds()
        {
            foreach (var instance in _soundInstances.Values)
            {
                instance.Stop();
            }
        }
        
        public void SetMasterVolume(float volume)
        {
            SoundEffect.MasterVolume = Math.Max(0f, Math.Min(1f, volume));
        }
        
        public void SetInstanceVolume(string instanceName, float volume)
        {
            if (_soundInstances.ContainsKey(instanceName))
            {
                _soundInstances[instanceName].Volume = Math.Max(0f, Math.Min(1f, volume));
            }
        }
        
        // Placeholder methods for sound effects that would trigger in game events
        public void PlayJumpSound()
        {
            PlaySound("jump", 0.7f);
        }
        
        public void PlayCoinSound()
        {
            PlaySound("coin", 0.8f);
        }
        
        public void PlayCrashSound()
        {
            PlaySound("crash", 1.0f);
        }
        
        public void StartBackgroundMusic()
        {
            PlaySoundInstance("background");
        }
        
        public void StopBackgroundMusic()
        {
            StopSoundInstance("background");
        }
        
        public void Dispose()
        {
            foreach (var instance in _soundInstances.Values)
            {
                instance?.Dispose();
            }
            _soundInstances.Clear();
            _soundEffects.Clear();
        }
    }
}
