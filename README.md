# لعبة Subway Surfers 2D

لعبة ثنائية الأبعاد مستوحاة من Subway Surfers مبنية باستخدام C# و MonoGame.

## المتطلبات

- Visual Studio 2019 أو أحدث
- .NET 6.0 أو أحدث
- MonoGame Framework

## تثبيت المتطلبات

### 1. تثبيت .NET SDK
قم بتحميل وتثبيت .NET 6.0 SDK من الموقع الرسمي:
https://dotnet.microsoft.com/download

### 2. تثبيت MonoGame
```bash
dotnet new install MonoGame.Templates.CSharp
```

## تشغيل المشروع

### الطريقة الأولى: باستخدام Visual Studio
1. افتح Visual Studio
2. اختر "Open a project or solution"
3. اختر ملف `SubwaySurfers2D.csproj`
4. اضغط F5 أو اختر "Debug > Start Debugging"

### الطريقة الثانية: باستخدام Command Line
```bash
cd path/to/project
dotnet restore
dotnet run
```

## طريقة اللعب

### التحكم
- **A** أو **←**: الحركة يساراً
- **D** أو **→**: الحركة يميناً  
- **W** أو **↑** أو **Space**: القفز
- **S** أو **↓**: الانزلاق
- **P**: إيقاف/استئناف اللعبة
- **R**: إعادة تشغيل اللعبة (عند انتهاء اللعبة)
- **ESC**: الخروج من اللعبة

### الهدف
- تجنب العقبات عن طريق القفز أو الانزلاق أو تغيير المسار
- جمع العملات الذهبية لزيادة النقاط
- البقاء على قيد الحياة أطول فترة ممكنة

### أنواع العقبات
- **عقبات منخفضة (حمراء)**: يمكن القفز فوقها
- **عقبات عالية (برتقالية)**: يمكن الانزلاق تحتها
- **عقبات كاملة (حمراء داكنة)**: يجب تجنبها بتغيير المسار

## هيكل المشروع

```
SubwaySurfers2D/
├── Game1.cs              # الكلاس الرئيسي للعبة
├── Player.cs             # كلاس اللاعب
├── Obstacle.cs           # كلاس العقبات
├── Coin.cs               # كلاس العملات
├── GameManager.cs        # مدير اللعبة
├── UI.cs                 # واجهة المستخدم
├── AudioManager.cs       # مدير الأصوات
├── Program.cs            # نقطة دخول البرنامج
├── SubwaySurfers2D.csproj # ملف المشروع
└── Content/
    └── Content.mgcb      # ملف المحتوى
```

## الميزات

- ✅ حركة اللاعب بين ثلاث مسارات
- ✅ نظام القفز والانزلاق
- ✅ عقبات متنوعة مع أنواع مختلفة
- ✅ جمع العملات
- ✅ نظام النقاط
- ✅ زيادة الصعوبة تدريجياً
- ✅ واجهة مستخدم كاملة
- ✅ نظام إيقاف/استئناف
- ✅ إعادة تشغيل اللعبة
- ✅ نظام أصوات (جاهز للملفات الصوتية)

## إضافة الرسوميات والأصوات

### إضافة الصور
1. ضع ملفات PNG في مجلد `Content`
2. افتح `Content.mgcb` باستخدام MonoGame Content Pipeline Tool
3. أضف الصور وقم ببناء المحتوى
4. حدث الكود لتحميل الصور:
```csharp
_playerTexture = Content.Load<Texture2D>("player");
```

### إضافة الأصوات
1. ضع ملفات WAV في مجلد `Content/Sounds`
2. أضفها إلى Content Pipeline
3. حدث `AudioManager.LoadContent()`:
```csharp
_soundEffects["jump"] = content.Load<SoundEffect>("Sounds/jump");
```

## التطوير المستقبلي

### ميزات مقترحة
- [ ] شخصيات متعددة
- [ ] قوى خاصة (مغناطيس، ضعف الجاذبية، إلخ)
- [ ] مستويات مختلفة
- [ ] متجر لشراء الترقيات
- [ ] نظام الإنجازات
- [ ] حفظ أفضل النتائج
- [ ] مؤثرات بصرية متقدمة
- [ ] موسيقى خلفية

### تحسينات تقنية
- [ ] تحسين الأداء
- [ ] إضافة تأثيرات الجسيمات
- [ ] تحسين الرسوميات
- [ ] إضافة الرسوم المتحركة
- [ ] تحسين نظام الاصطدام

## استكشاف الأخطاء

### مشاكل شائعة

**خطأ: "MonoGame.Framework not found"**
- تأكد من تثبيت MonoGame Framework
- قم بتشغيل `dotnet restore`

**خطأ: "Content not found"**
- تأكد من وجود مجلد Content
- تأكد من بناء المحتوى بشكل صحيح

**اللعبة بطيئة**
- تأكد من تشغيل اللعبة في وضع Release
- تحقق من مواصفات الجهاز

## المساهمة

لتحسين اللعبة:
1. قم بعمل Fork للمشروع
2. أنشئ branch جديد للميزة
3. اكتب الكود مع التعليقات
4. اختبر التغييرات
5. أرسل Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتطوير.
