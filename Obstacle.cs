using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;

namespace SubwaySurfers2D
{
    public enum ObstacleType
    {
        Low,    // يمكن القفز فوقها
        High,   // يمكن الانزلاق تحتها
        Full    // يجب تجنبها بتغيير المسار
    }
    
    public class Obstacle
    {
        private Texture2D _texture;
        private Vector2 _position;
        private Vector2 _velocity;
        private ObstacleType _type;
        private Color _color;
        
        // Movement settings
        private const float MOVE_SPEED = 200f;
        
        public Vector2 Position => _position;
        public ObstacleType Type => _type;
        
        public Obstacle(Texture2D texture, Vector2 startPosition, ObstacleType type)
        {
            _texture = texture;
            _position = startPosition;
            _type = type;
            _velocity = new Vector2(0, MOVE_SPEED);
            
            // Set color based on type
            switch (_type)
            {
                case ObstacleType.Low:
                    _color = Color.Red;
                    break;
                case ObstacleType.High:
                    _color = Color.Orange;
                    break;
                case ObstacleType.Full:
                    _color = Color.DarkRed;
                    break;
            }
        }
        
        public void Update(GameTime gameTime)
        {
            float deltaTime = (float)gameTime.ElapsedGameTime.TotalSeconds;
            
            // Move obstacle down the screen
            _position += _velocity * deltaTime;
        }
        
        public void Draw(SpriteBatch spriteBatch)
        {
            Rectangle destRect = new Rectangle(
                (int)_position.X, 
                (int)_position.Y, 
                _texture.Width, 
                GetHeight()
            );
            
            spriteBatch.Draw(_texture, destRect, _color);
        }
        
        private int GetHeight()
        {
            switch (_type)
            {
                case ObstacleType.Low:
                    return _texture.Height / 2; // نصف الارتفاع
                case ObstacleType.High:
                    return _texture.Height; // الارتفاع الكامل
                case ObstacleType.Full:
                    return _texture.Height; // الارتفاع الكامل
                default:
                    return _texture.Height;
            }
        }
        
        public Rectangle GetBounds()
        {
            return new Rectangle(
                (int)_position.X, 
                (int)_position.Y, 
                _texture.Width, 
                GetHeight()
            );
        }
        
        // تحقق من إمكانية تجنب العقبة بالقفز
        public bool CanJumpOver()
        {
            return _type == ObstacleType.Low;
        }
        
        // تحقق من إمكانية تجنب العقبة بالانزلاق
        public bool CanSlideUnder()
        {
            return _type == ObstacleType.High;
        }
        
        // إنشاء عقبة عشوائية في مسار محدد
        public static Obstacle CreateRandomObstacle(Texture2D texture, int lane)
        {
            // تحديد موقع المسار
            float x = (lane * Game1.LANE_WIDTH) + (Game1.LANE_WIDTH / 2) - (texture.Width / 2);
            Vector2 position = new Vector2(x, -texture.Height);
            
            // اختيار نوع عشوائي للعقبة
            System.Random random = new System.Random();
            ObstacleType[] types = { ObstacleType.Low, ObstacleType.High, ObstacleType.Full };
            ObstacleType randomType = types[random.Next(types.Length)];
            
            return new Obstacle(texture, position, randomType);
        }
    }
}
