using System;
using System.Drawing;
using System.Windows.Forms;
using System.Collections.Generic;

namespace SimpleSubwaySurfers
{
    public partial class GameForm : Form
    {
        private Timer gameTimer;
        private Player player;
        private List<Obstacle> obstacles;
        private List<Coin> coins;
        private Random random;
        private int score;
        private int gameSpeed;
        private bool gameRunning;
        
        // Game constants
        private const int LANE_WIDTH = 200;
        private const int PLAYER_SIZE = 40;
        private const int OBSTACLE_SIZE = 50;
        private const int COIN_SIZE = 20;
        
        public GameForm()
        {
            InitializeComponent();
            InitializeGame();
        }
        
        private void InitializeComponent()
        {
            this.Text = "Subway Surfers 2D - Simple Version";
            this.Size = new Size(800, 600);
            this.BackColor = Color.DarkGreen;
            this.KeyPreview = true;
            this.DoubleBuffered = true;
            this.StartPosition = FormStartPosition.CenterScreen;
            
            this.KeyDown += GameForm_KeyDown;
            this.Paint += GameForm_Paint;
        }
        
        private void InitializeGame()
        {
            player = new Player(LANE_WIDTH + LANE_WIDTH / 2, this.Height - 100);
            obstacles = new List<Obstacle>();
            coins = new List<Coin>();
            random = new Random();
            score = 0;
            gameSpeed = 5;
            gameRunning = true;
            
            gameTimer = new Timer();
            gameTimer.Interval = 50; // 20 FPS
            gameTimer.Tick += GameTimer_Tick;
            gameTimer.Start();
        }
        
        private void GameTimer_Tick(object sender, EventArgs e)
        {
            if (!gameRunning) return;
            
            UpdateGame();
            this.Invalidate(); // Trigger repaint
        }
        
        private void UpdateGame()
        {
            // Update obstacles
            for (int i = obstacles.Count - 1; i >= 0; i--)
            {
                obstacles[i].Y += gameSpeed;
                if (obstacles[i].Y > this.Height)
                {
                    obstacles.RemoveAt(i);
                }
            }
            
            // Update coins
            for (int i = coins.Count - 1; i >= 0; i--)
            {
                coins[i].Y += gameSpeed;
                if (coins[i].Y > this.Height)
                {
                    coins.RemoveAt(i);
                }
            }
            
            // Spawn new obstacles
            if (random.Next(100) < 3) // 3% chance each frame
            {
                int lane = random.Next(3);
                obstacles.Add(new Obstacle(lane * LANE_WIDTH + LANE_WIDTH / 2, -OBSTACLE_SIZE));
            }
            
            // Spawn new coins
            if (random.Next(100) < 5) // 5% chance each frame
            {
                int lane = random.Next(3);
                coins.Add(new Coin(lane * LANE_WIDTH + LANE_WIDTH / 2, -COIN_SIZE));
            }
            
            // Check collisions
            CheckCollisions();
            
            // Increase score and speed
            score += 1;
            if (score % 500 == 0)
            {
                gameSpeed++;
            }
        }
        
        private void CheckCollisions()
        {
            Rectangle playerRect = new Rectangle(player.X - PLAYER_SIZE/2, player.Y - PLAYER_SIZE/2, PLAYER_SIZE, PLAYER_SIZE);
            
            // Check obstacle collisions
            foreach (var obstacle in obstacles)
            {
                Rectangle obstacleRect = new Rectangle(obstacle.X - OBSTACLE_SIZE/2, obstacle.Y - OBSTACLE_SIZE/2, OBSTACLE_SIZE, OBSTACLE_SIZE);
                if (playerRect.IntersectsWith(obstacleRect))
                {
                    GameOver();
                    return;
                }
            }
            
            // Check coin collisions
            for (int i = coins.Count - 1; i >= 0; i--)
            {
                Rectangle coinRect = new Rectangle(coins[i].X - COIN_SIZE/2, coins[i].Y - COIN_SIZE/2, COIN_SIZE, COIN_SIZE);
                if (playerRect.IntersectsWith(coinRect))
                {
                    score += 50;
                    coins.RemoveAt(i);
                }
            }
        }
        
        private void GameOver()
        {
            gameRunning = false;
            gameTimer.Stop();
            MessageBox.Show($"Game Over!\nScore: {score}\n\nPress R to restart or close to exit.", "Game Over");
        }
        
        private void GameForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (!gameRunning)
            {
                if (e.KeyCode == Keys.R)
                {
                    RestartGame();
                }
                return;
            }
            
            switch (e.KeyCode)
            {
                case Keys.Left:
                case Keys.A:
                    player.MoveLeft();
                    break;
                case Keys.Right:
                case Keys.D:
                    player.MoveRight();
                    break;
                case Keys.Escape:
                    this.Close();
                    break;
            }
        }
        
        private void RestartGame()
        {
            obstacles.Clear();
            coins.Clear();
            player.Reset(LANE_WIDTH + LANE_WIDTH / 2, this.Height - 100);
            score = 0;
            gameSpeed = 5;
            gameRunning = true;
            gameTimer.Start();
        }
        
        private void GameForm_Paint(object sender, PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            
            // Draw lane dividers
            using (Pen whitePen = new Pen(Color.White, 3))
            {
                g.DrawLine(whitePen, LANE_WIDTH, 0, LANE_WIDTH, this.Height);
                g.DrawLine(whitePen, LANE_WIDTH * 2, 0, LANE_WIDTH * 2, this.Height);
            }
            
            // Draw player
            using (Brush blueBrush = new SolidBrush(Color.Blue))
            {
                g.FillRectangle(blueBrush, player.X - PLAYER_SIZE/2, player.Y - PLAYER_SIZE/2, PLAYER_SIZE, PLAYER_SIZE);
            }
            
            // Draw obstacles
            using (Brush redBrush = new SolidBrush(Color.Red))
            {
                foreach (var obstacle in obstacles)
                {
                    g.FillRectangle(redBrush, obstacle.X - OBSTACLE_SIZE/2, obstacle.Y - OBSTACLE_SIZE/2, OBSTACLE_SIZE, OBSTACLE_SIZE);
                }
            }
            
            // Draw coins
            using (Brush goldBrush = new SolidBrush(Color.Gold))
            {
                foreach (var coin in coins)
                {
                    g.FillEllipse(goldBrush, coin.X - COIN_SIZE/2, coin.Y - COIN_SIZE/2, COIN_SIZE, COIN_SIZE);
                }
            }
            
            // Draw UI
            using (Brush whiteBrush = new SolidBrush(Color.White))
            using (Font font = new Font("Arial", 16, FontStyle.Bold))
            {
                g.DrawString($"Score: {score}", font, whiteBrush, 10, 10);
                g.DrawString($"Speed: {gameSpeed}", font, whiteBrush, 10, 40);
                g.DrawString("Controls: A/D or ←/→ to move, ESC to exit", new Font("Arial", 10), whiteBrush, 10, this.Height - 30);
            }
            
            if (!gameRunning)
            {
                using (Brush blackBrush = new SolidBrush(Color.FromArgb(128, Color.Black)))
                using (Brush whiteBrush = new SolidBrush(Color.White))
                using (Font font = new Font("Arial", 24, FontStyle.Bold))
                {
                    g.FillRectangle(blackBrush, 0, 0, this.Width, this.Height);
                    string gameOverText = "GAME OVER";
                    SizeF textSize = g.MeasureString(gameOverText, font);
                    g.DrawString(gameOverText, font, whiteBrush, 
                        (this.Width - textSize.Width) / 2, (this.Height - textSize.Height) / 2);
                }
            }
        }
    }
    
    public class Player
    {
        public int X { get; private set; }
        public int Y { get; private set; }
        private int currentLane;
        
        public Player(int x, int y)
        {
            X = x;
            Y = y;
            currentLane = 1; // Start in middle lane
        }
        
        public void MoveLeft()
        {
            if (currentLane > 0)
            {
                currentLane--;
                X = currentLane * LANE_WIDTH + LANE_WIDTH / 2;
            }
        }
        
        public void MoveRight()
        {
            if (currentLane < 2)
            {
                currentLane++;
                X = currentLane * LANE_WIDTH + LANE_WIDTH / 2;
            }
        }
        
        public void Reset(int x, int y)
        {
            X = x;
            Y = y;
            currentLane = 1;
        }
        
        private const int LANE_WIDTH = 200;
    }
    
    public class Obstacle
    {
        public int X { get; set; }
        public int Y { get; set; }
        
        public Obstacle(int x, int y)
        {
            X = x;
            Y = y;
        }
    }
    
    public class Coin
    {
        public int X { get; set; }
        public int Y { get; set; }
        
        public Coin(int x, int y)
        {
            X = x;
            Y = y;
        }
    }
}
