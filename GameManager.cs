using Microsoft.Xna.Framework;
using System;
using System.Collections.Generic;

namespace SubwaySurfers2D
{
    public enum GameState
    {
        Playing,
        GameOver,
        Paused
    }
    
    public class GameManager
    {
        private GameState _gameState;
        private int _score;
        private int _coinsCollected;
        private float _gameSpeed;
        private float _obstacleSpawnTimer;
        private float _coinSpawnTimer;
        private Random _random;
        
        // Spawn settings
        private const float INITIAL_OBSTACLE_SPAWN_INTERVAL = 2f;
        private const float INITIAL_COIN_SPAWN_INTERVAL = 1.5f;
        private const float MIN_OBSTACLE_SPAWN_INTERVAL = 0.8f;
        private const float MIN_COIN_SPAWN_INTERVAL = 0.5f;
        private const float SPEED_INCREASE_RATE = 0.1f;
        private const float DIFFICULTY_INCREASE_INTERVAL = 10f;
        
        // Scoring
        private const int COIN_SCORE_VALUE = 10;
        private const int DISTANCE_SCORE_MULTIPLIER = 1;
        
        private float _difficultyTimer;
        private float _currentObstacleSpawnInterval;
        private float _currentCoinSpawnInterval;
        
        public GameState State => _gameState;
        public int Score => _score;
        public int CoinsCollected => _coinsCollected;
        public float GameSpeed => _gameSpeed;
        
        public GameManager()
        {
            _gameState = GameState.Playing;
            _score = 0;
            _coinsCollected = 0;
            _gameSpeed = 1f;
            _obstacleSpawnTimer = 0f;
            _coinSpawnTimer = 0f;
            _difficultyTimer = 0f;
            _random = new Random();
            
            _currentObstacleSpawnInterval = INITIAL_OBSTACLE_SPAWN_INTERVAL;
            _currentCoinSpawnInterval = INITIAL_COIN_SPAWN_INTERVAL;
        }
        
        public void Update(GameTime gameTime, List<Obstacle> obstacles, List<Coin> coins, 
                          Microsoft.Xna.Framework.Graphics.Texture2D obstacleTexture, 
                          Microsoft.Xna.Framework.Graphics.Texture2D coinTexture)
        {
            if (_gameState != GameState.Playing)
                return;
                
            float deltaTime = (float)gameTime.ElapsedGameTime.TotalSeconds;
            
            // Update timers
            _obstacleSpawnTimer += deltaTime;
            _coinSpawnTimer += deltaTime;
            _difficultyTimer += deltaTime;
            
            // Increase difficulty over time
            UpdateDifficulty();
            
            // Spawn obstacles
            if (_obstacleSpawnTimer >= _currentObstacleSpawnInterval)
            {
                SpawnObstacle(obstacles, obstacleTexture);
                _obstacleSpawnTimer = 0f;
            }
            
            // Spawn coins
            if (_coinSpawnTimer >= _currentCoinSpawnInterval)
            {
                SpawnCoins(coins, coinTexture);
                _coinSpawnTimer = 0f;
            }
            
            // Update score based on distance traveled
            _score += (int)(DISTANCE_SCORE_MULTIPLIER * _gameSpeed * deltaTime * 10);
        }
        
        private void UpdateDifficulty()
        {
            if (_difficultyTimer >= DIFFICULTY_INCREASE_INTERVAL)
            {
                // Increase game speed
                _gameSpeed += SPEED_INCREASE_RATE;
                
                // Decrease spawn intervals (increase spawn rate)
                _currentObstacleSpawnInterval = Math.Max(
                    MIN_OBSTACLE_SPAWN_INTERVAL, 
                    _currentObstacleSpawnInterval - 0.1f
                );
                
                _currentCoinSpawnInterval = Math.Max(
                    MIN_COIN_SPAWN_INTERVAL, 
                    _currentCoinSpawnInterval - 0.05f
                );
                
                _difficultyTimer = 0f;
            }
        }
        
        private void SpawnObstacle(List<Obstacle> obstacles, Microsoft.Xna.Framework.Graphics.Texture2D texture)
        {
            // Choose a random lane
            int lane = _random.Next(0, 3);
            
            // Make sure we don't spawn obstacles too close to each other in the same lane
            bool canSpawn = true;
            foreach (var obstacle in obstacles)
            {
                float obstacleX = (lane * Game1.LANE_WIDTH) + (Game1.LANE_WIDTH / 2);
                if (Math.Abs(obstacle.Position.X - obstacleX) < Game1.LANE_WIDTH / 2 && 
                    obstacle.Position.Y < 100)
                {
                    canSpawn = false;
                    break;
                }
            }
            
            if (canSpawn)
            {
                obstacles.Add(Obstacle.CreateRandomObstacle(texture, lane));
            }
        }
        
        private void SpawnCoins(List<Coin> coins, Microsoft.Xna.Framework.Graphics.Texture2D texture)
        {
            // Randomly choose spawn pattern
            int spawnType = _random.Next(0, 4);
            
            switch (spawnType)
            {
                case 0: // Single coin
                    {
                        int lane = _random.Next(0, 3);
                        coins.Add(Coin.CreateCoin(texture, lane));
                    }
                    break;
                    
                case 1: // Line of coins in one lane
                    {
                        int lane = _random.Next(0, 3);
                        var coinLine = Coin.CreateCoinLine(texture, lane, 3);
                        coins.AddRange(coinLine);
                    }
                    break;
                    
                case 2: // Coins in all lanes
                    {
                        var coinPattern = Coin.CreateCoinPattern(texture);
                        coins.AddRange(coinPattern);
                    }
                    break;
                    
                case 3: // Two lanes with coins
                    {
                        int lane1 = _random.Next(0, 3);
                        int lane2;
                        do
                        {
                            lane2 = _random.Next(0, 3);
                        } while (lane2 == lane1);
                        
                        coins.Add(Coin.CreateCoin(texture, lane1));
                        coins.Add(Coin.CreateCoin(texture, lane2));
                    }
                    break;
            }
        }
        
        public void CollectCoin()
        {
            _coinsCollected++;
            _score += COIN_SCORE_VALUE;
        }
        
        public void GameOver()
        {
            _gameState = GameState.GameOver;
        }
        
        public void RestartGame()
        {
            _gameState = GameState.Playing;
            _score = 0;
            _coinsCollected = 0;
            _gameSpeed = 1f;
            _obstacleSpawnTimer = 0f;
            _coinSpawnTimer = 0f;
            _difficultyTimer = 0f;
            
            _currentObstacleSpawnInterval = INITIAL_OBSTACLE_SPAWN_INTERVAL;
            _currentCoinSpawnInterval = INITIAL_COIN_SPAWN_INTERVAL;
        }
        
        public void PauseGame()
        {
            if (_gameState == GameState.Playing)
                _gameState = GameState.Paused;
        }
        
        public void ResumeGame()
        {
            if (_gameState == GameState.Paused)
                _gameState = GameState.Playing;
        }
    }
}
