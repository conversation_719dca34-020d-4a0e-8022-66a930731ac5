using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Microsoft.Xna.Framework.Input;

namespace SubwaySurfers2D
{
    public class Player
    {
        private Texture2D _texture;
        private Vector2 _position;
        private Vector2 _velocity;
        private int _currentLane; // 0 = left, 1 = center, 2 = right
        private bool _isJumping;
        private bool _isSliding;
        private float _jumpHeight;
        private float _slideTimer;
        private AudioManager _audioManager;
        
        // Movement settings
        private const float MOVE_SPEED = 300f;
        private const float JUMP_SPEED = 400f;
        private const float GRAVITY = 800f;
        private const float SLIDE_DURATION = 0.5f;
        
        // Input handling
        private KeyboardState _previousKeyboardState;
        private KeyboardState _currentKeyboardState;
        
        public Vector2 Position => _position;
        public bool IsSliding => _isSliding;
        
        public Player(Texture2D texture, Vector2 startPosition, AudioManager audioManager = null)
        {
            _texture = texture;
            _position = startPosition;
            _currentLane = 1; // Start in center lane
            _velocity = Vector2.Zero;
            _isJumping = false;
            _isSliding = false;
            _jumpHeight = 0f;
            _slideTimer = 0f;
            _audioManager = audioManager;

            // Set initial position to center lane
            _position.X = Game1.LANE_WIDTH + (Game1.LANE_WIDTH / 2) - (_texture.Width / 2);
        }
        
        public void Update(GameTime gameTime)
        {
            float deltaTime = (float)gameTime.ElapsedGameTime.TotalSeconds;
            
            // Update input states
            _previousKeyboardState = _currentKeyboardState;
            _currentKeyboardState = Keyboard.GetState();
            
            // Handle lane switching
            HandleLaneMovement();
            
            // Handle jumping
            HandleJumping(deltaTime);
            
            // Handle sliding
            HandleSliding(deltaTime);
            
            // Update position
            UpdatePosition(deltaTime);
        }
        
        private void HandleLaneMovement()
        {
            // Move left (A key or Left arrow)
            if ((_currentKeyboardState.IsKeyDown(Keys.Left) || _currentKeyboardState.IsKeyDown(Keys.A)) &&
                (!_previousKeyboardState.IsKeyDown(Keys.Left) && !_previousKeyboardState.IsKeyDown(Keys.A)) &&
                _currentLane > 0)
            {
                _currentLane--;
                UpdateLanePosition();
            }

            // Move right (D key or Right arrow)
            if ((_currentKeyboardState.IsKeyDown(Keys.Right) || _currentKeyboardState.IsKeyDown(Keys.D)) &&
                (!_previousKeyboardState.IsKeyDown(Keys.Right) && !_previousKeyboardState.IsKeyDown(Keys.D)) &&
                _currentLane < 2)
            {
                _currentLane++;
                UpdateLanePosition();
            }
        }
        
        private void UpdateLanePosition()
        {
            float targetX = (_currentLane * Game1.LANE_WIDTH) + (Game1.LANE_WIDTH / 2) - (_texture.Width / 2);
            _position.X = targetX;
        }
        
        private void HandleJumping(float deltaTime)
        {
            // Start jump (W key, Up arrow, or Spacebar)
            if ((_currentKeyboardState.IsKeyDown(Keys.Up) || _currentKeyboardState.IsKeyDown(Keys.W) || _currentKeyboardState.IsKeyDown(Keys.Space)) &&
                (!_previousKeyboardState.IsKeyDown(Keys.Up) && !_previousKeyboardState.IsKeyDown(Keys.W) && !_previousKeyboardState.IsKeyDown(Keys.Space)) &&
                !_isJumping && !_isSliding)
            {
                _isJumping = true;
                _velocity.Y = -JUMP_SPEED;
                _jumpHeight = 0f;
                _audioManager?.PlayJumpSound();
            }

            // Apply gravity when jumping
            if (_isJumping)
            {
                _velocity.Y += GRAVITY * deltaTime;
                _jumpHeight += _velocity.Y * deltaTime;

                // Land
                if (_jumpHeight >= 0)
                {
                    _isJumping = false;
                    _velocity.Y = 0;
                    _jumpHeight = 0;
                }
            }
        }
        
        private void HandleSliding(float deltaTime)
        {
            // Start slide (S key or Down arrow)
            if ((_currentKeyboardState.IsKeyDown(Keys.Down) || _currentKeyboardState.IsKeyDown(Keys.S)) &&
                (!_previousKeyboardState.IsKeyDown(Keys.Down) && !_previousKeyboardState.IsKeyDown(Keys.S)) &&
                !_isJumping && !_isSliding)
            {
                _isSliding = true;
                _slideTimer = SLIDE_DURATION;
            }

            // Update slide timer
            if (_isSliding)
            {
                _slideTimer -= deltaTime;
                if (_slideTimer <= 0)
                {
                    _isSliding = false;
                    _slideTimer = 0;
                }
            }
        }
        
        private void UpdatePosition(float deltaTime)
        {
            // Update Y position for jumping
            if (_isJumping)
            {
                _position.Y += _velocity.Y * deltaTime;
            }
        }
        
        public void Draw(SpriteBatch spriteBatch)
        {
            Vector2 drawPosition = _position;
            
            // Adjust draw position for jumping
            if (_isJumping)
            {
                drawPosition.Y -= _jumpHeight;
            }
            
            // Adjust size for sliding
            Rectangle sourceRect = new Rectangle(0, 0, _texture.Width, _texture.Height);
            Rectangle destRect = new Rectangle(
                (int)drawPosition.X, 
                (int)drawPosition.Y, 
                _texture.Width, 
                _isSliding ? _texture.Height / 2 : _texture.Height
            );
            
            if (_isSliding)
            {
                destRect.Y += _texture.Height / 2; // Move down when sliding
            }
            
            spriteBatch.Draw(_texture, destRect, sourceRect, Color.White);
        }
        
        public Rectangle GetBounds()
        {
            Rectangle bounds = new Rectangle(
                (int)_position.X, 
                (int)(_position.Y - (_isJumping ? _jumpHeight : 0)), 
                _texture.Width, 
                _isSliding ? _texture.Height / 2 : _texture.Height
            );
            
            if (_isSliding)
            {
                bounds.Y += _texture.Height / 2;
            }
            
            return bounds;
        }
    }
}
