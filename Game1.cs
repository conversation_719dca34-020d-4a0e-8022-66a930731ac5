using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Microsoft.Xna.Framework.Input;
using System.Collections.Generic;

namespace SubwaySurfers2D
{
    public class Game1 : Game
    {
        private GraphicsDeviceManager _graphics;
        private SpriteBatch _spriteBatch;
        
        // Game objects
        private Player _player;
        private List<Obstacle> _obstacles;
        private List<Coin> _coins;
        private GameManager _gameManager;
        private UI _ui;
        private AudioManager _audioManager;
        
        // Textures
        private Texture2D _playerTexture;
        private Texture2D _obstacleTexture;
        private Texture2D _coinTexture;
        private Texture2D _backgroundTexture;
        private Texture2D _roadTexture;
        
        // Fonts
        private SpriteFont _font;
        
        // Game settings
        public const int SCREEN_WIDTH = 800;
        public const int SCREEN_HEIGHT = 600;
        public const int LANE_WIDTH = SCREEN_WIDTH / 3;
        
        public Game1()
        {
            _graphics = new GraphicsDeviceManager(this);
            Content.RootDirectory = "Content";
            IsMouseVisible = true;
            
            _graphics.PreferredBackBufferWidth = SCREEN_WIDTH;
            _graphics.PreferredBackBufferHeight = SCREEN_HEIGHT;
        }

        protected override void Initialize()
        {
            // Initialize game objects
            _obstacles = new List<Obstacle>();
            _coins = new List<Coin>();
            _gameManager = new GameManager();
            _ui = new UI(_gameManager);
            _audioManager = new AudioManager();

            base.Initialize();
        }

        protected override void LoadContent()
        {
            _spriteBatch = new SpriteBatch(GraphicsDevice);
            
            // Create simple colored textures for now
            _playerTexture = CreateColorTexture(Color.Blue, 40, 60);
            _obstacleTexture = CreateColorTexture(Color.Red, 50, 50);
            _coinTexture = CreateColorTexture(Color.Yellow, 20, 20);
            _backgroundTexture = CreateColorTexture(Color.Green, SCREEN_WIDTH, SCREEN_HEIGHT);
            _roadTexture = CreateColorTexture(Color.Gray, SCREEN_WIDTH, SCREEN_HEIGHT);
            
            // Create a simple font (we'll use the default font for now)
            // _font = Content.Load<SpriteFont>("DefaultFont");
            
            // Initialize player
            _player = new Player(_playerTexture, new Vector2(SCREEN_WIDTH / 2, SCREEN_HEIGHT - 100), _audioManager);

            // Initialize UI and Audio
            _ui.LoadContent(GraphicsDevice, _font);
            _audioManager.LoadContent(Content);

            // Start background music
            _audioManager.StartBackgroundMusic();
        }
        
        private Texture2D CreateColorTexture(Color color, int width, int height)
        {
            Texture2D texture = new Texture2D(GraphicsDevice, width, height);
            Color[] data = new Color[width * height];
            for (int i = 0; i < data.Length; i++)
                data[i] = color;
            texture.SetData(data);
            return texture;
        }

        protected override void Update(GameTime gameTime)
        {
            KeyboardState keyboardState = Keyboard.GetState();

            if (GamePad.GetState(PlayerIndex.One).Buttons.Back == ButtonState.Pressed ||
                keyboardState.IsKeyDown(Keys.Escape))
                Exit();

            // Handle game state input
            HandleGameStateInput(keyboardState);

            // Only update game objects when playing
            if (_gameManager.State == GameState.Playing)
            {
                // Update game objects
                _player.Update(gameTime);
                _gameManager.Update(gameTime, _obstacles, _coins, _obstacleTexture, _coinTexture);

                // Update obstacles
                for (int i = _obstacles.Count - 1; i >= 0; i--)
                {
                    _obstacles[i].Update(gameTime);
                    if (_obstacles[i].Position.Y > SCREEN_HEIGHT)
                    {
                        _obstacles.RemoveAt(i);
                    }
                }

                // Update coins
                for (int i = _coins.Count - 1; i >= 0; i--)
                {
                    _coins[i].Update(gameTime);
                    if (_coins[i].Position.Y > SCREEN_HEIGHT)
                    {
                        _coins.RemoveAt(i);
                    }
                }

                // Check collisions
                CheckCollisions();
            }

            base.Update(gameTime);
        }

        private void HandleGameStateInput(KeyboardState keyboardState)
        {
            // Handle restart
            if (_gameManager.State == GameState.GameOver && keyboardState.IsKeyDown(Keys.R))
            {
                RestartGame();
            }

            // Handle pause/resume
            if (keyboardState.IsKeyDown(Keys.P))
            {
                if (_gameManager.State == GameState.Playing)
                {
                    _gameManager.PauseGame();
                    _audioManager.PauseAllSounds();
                }
                else if (_gameManager.State == GameState.Paused)
                {
                    _gameManager.ResumeGame();
                    _audioManager.ResumeAllSounds();
                }
            }
        }

        private void RestartGame()
        {
            // Clear all game objects
            _obstacles.Clear();
            _coins.Clear();

            // Reset game manager
            _gameManager.RestartGame();

            // Reset player
            _player = new Player(_playerTexture, new Vector2(SCREEN_WIDTH / 2, SCREEN_HEIGHT - 100), _audioManager);
        }
        
        private void CheckCollisions()
        {
            // Check obstacle collisions with smart collision detection
            for (int i = _obstacles.Count - 1; i >= 0; i--)
            {
                var obstacle = _obstacles[i];
                var playerBounds = _player.GetBounds();
                var obstacleBounds = obstacle.GetBounds();

                if (playerBounds.Intersects(obstacleBounds))
                {
                    // Check if player can avoid obstacle
                    bool canAvoid = false;

                    // Can jump over low obstacles
                    if (obstacle.CanJumpOver() && _player.Position.Y < obstacle.Position.Y)
                    {
                        canAvoid = true;
                    }

                    // Can slide under high obstacles
                    if (obstacle.CanSlideUnder() && _player.IsSliding)
                    {
                        canAvoid = true;
                    }

                    if (!canAvoid)
                    {
                        _audioManager.PlayCrashSound();
                        _gameManager.GameOver();
                        break;
                    }
                }
            }

            // Check coin collisions
            for (int i = _coins.Count - 1; i >= 0; i--)
            {
                if (_player.GetBounds().Intersects(_coins[i].GetBounds()))
                {
                    _audioManager.PlayCoinSound();
                    _gameManager.CollectCoin();
                    _coins.RemoveAt(i);
                }
            }
        }

        protected override void Draw(GameTime gameTime)
        {
            GraphicsDevice.Clear(Color.CornflowerBlue);

            _spriteBatch.Begin();
            
            // Draw background
            _spriteBatch.Draw(_backgroundTexture, Vector2.Zero, Color.White);
            
            // Draw lane dividers
            DrawLaneDividers();
            
            // Draw game objects
            _player.Draw(_spriteBatch);
            
            foreach (var obstacle in _obstacles)
                obstacle.Draw(_spriteBatch);
                
            foreach (var coin in _coins)
                coin.Draw(_spriteBatch);
            
            // Draw UI
            _ui.Draw(_spriteBatch);

            _spriteBatch.End();

            base.Draw(gameTime);
        }
        
        private void DrawLaneDividers()
        {
            // Draw vertical lines to separate lanes
            var lineTexture = CreateColorTexture(Color.White, 2, SCREEN_HEIGHT);
            _spriteBatch.Draw(lineTexture, new Vector2(LANE_WIDTH, 0), Color.White);
            _spriteBatch.Draw(lineTexture, new Vector2(LANE_WIDTH * 2, 0), Color.White);
        }

        protected override void UnloadContent()
        {
            _audioManager?.Dispose();
            base.UnloadContent();
        }
    }
}
