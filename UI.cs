using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Microsoft.Xna.Framework.Input;

namespace SubwaySurfers2D
{
    public class UI
    {
        private SpriteFont _font;
        private Texture2D _pixelTexture;
        private GameManager _gameManager;
        
        // UI Colors
        private Color _textColor = Color.White;
        private Color _shadowColor = Color.Black;
        private Color _gameOverBackgroundColor = Color.Black * 0.7f;
        private Color _buttonColor = Color.DarkBlue;
        private Color _buttonHoverColor = Color.Blue;
        
        // UI Layout
        private const int MARGIN = 20;
        private const int SHADOW_OFFSET = 2;
        
        public UI(GameManager gameManager)
        {
            _gameManager = gameManager;
        }
        
        public void LoadContent(GraphicsDevice graphicsDevice, SpriteFont font = null)
        {
            _font = font;
            
            // Create a 1x1 white pixel texture for drawing rectangles
            _pixelTexture = new Texture2D(graphicsDevice, 1, 1);
            _pixelTexture.SetData(new[] { Color.White });
        }
        
        public void Draw(SpriteBatch spriteBatch)
        {
            switch (_gameManager.State)
            {
                case GameState.Playing:
                    DrawGameplayUI(spriteBatch);
                    break;
                case GameState.GameOver:
                    DrawGameplayUI(spriteBatch);
                    DrawGameOverScreen(spriteBatch);
                    break;
                case GameState.Paused:
                    DrawGameplayUI(spriteBatch);
                    DrawPauseScreen(spriteBatch);
                    break;
            }
        }
        
        private void DrawGameplayUI(SpriteBatch spriteBatch)
        {
            // Draw score
            string scoreText = $"Score: {_gameManager.Score}";
            DrawTextWithShadow(spriteBatch, scoreText, new Vector2(MARGIN, MARGIN), _textColor);
            
            // Draw coins collected
            string coinsText = $"Coins: {_gameManager.CoinsCollected}";
            DrawTextWithShadow(spriteBatch, coinsText, new Vector2(MARGIN, MARGIN + 30), _textColor);
            
            // Draw speed indicator
            string speedText = $"Speed: {_gameManager.GameSpeed:F1}x";
            DrawTextWithShadow(spriteBatch, speedText, new Vector2(MARGIN, MARGIN + 60), _textColor);
            
            // Draw controls hint
            DrawControlsHint(spriteBatch);
        }
        
        private void DrawControlsHint(SpriteBatch spriteBatch)
        {
            string[] controls = {
                "Controls:",
                "A/D or ←/→ - Move",
                "W/↑/Space - Jump", 
                "S/↓ - Slide",
                "ESC - Exit"
            };
            
            Vector2 position = new Vector2(Game1.SCREEN_WIDTH - 200, MARGIN);
            
            for (int i = 0; i < controls.Length; i++)
            {
                Color color = i == 0 ? Color.Yellow : Color.LightGray;
                DrawTextWithShadow(spriteBatch, controls[i], position + new Vector2(0, i * 20), color);
            }
        }
        
        private void DrawGameOverScreen(SpriteBatch spriteBatch)
        {
            // Draw semi-transparent background
            Rectangle screenRect = new Rectangle(0, 0, Game1.SCREEN_WIDTH, Game1.SCREEN_HEIGHT);
            spriteBatch.Draw(_pixelTexture, screenRect, _gameOverBackgroundColor);
            
            // Game Over text
            string gameOverText = "GAME OVER";
            Vector2 gameOverSize = MeasureString(gameOverText);
            Vector2 gameOverPos = new Vector2(
                (Game1.SCREEN_WIDTH - gameOverSize.X) / 2,
                Game1.SCREEN_HEIGHT / 2 - 100
            );
            DrawTextWithShadow(spriteBatch, gameOverText, gameOverPos, Color.Red, 2f);
            
            // Final score
            string finalScoreText = $"Final Score: {_gameManager.Score}";
            Vector2 scoreSize = MeasureString(finalScoreText);
            Vector2 scorePos = new Vector2(
                (Game1.SCREEN_WIDTH - scoreSize.X) / 2,
                gameOverPos.Y + 60
            );
            DrawTextWithShadow(spriteBatch, finalScoreText, scorePos, Color.White, 1.5f);
            
            // Coins collected
            string coinsText = $"Coins Collected: {_gameManager.CoinsCollected}";
            Vector2 coinsSize = MeasureString(coinsText);
            Vector2 coinsPos = new Vector2(
                (Game1.SCREEN_WIDTH - coinsSize.X) / 2,
                scorePos.Y + 40
            );
            DrawTextWithShadow(spriteBatch, coinsText, coinsPos, Color.Gold);
            
            // Restart instruction
            string restartText = "Press R to Restart or ESC to Exit";
            Vector2 restartSize = MeasureString(restartText);
            Vector2 restartPos = new Vector2(
                (Game1.SCREEN_WIDTH - restartSize.X) / 2,
                coinsPos.Y + 60
            );
            DrawTextWithShadow(spriteBatch, restartText, restartPos, Color.LightGreen);
        }
        
        private void DrawPauseScreen(SpriteBatch spriteBatch)
        {
            // Draw semi-transparent background
            Rectangle screenRect = new Rectangle(0, 0, Game1.SCREEN_WIDTH, Game1.SCREEN_HEIGHT);
            spriteBatch.Draw(_pixelTexture, screenRect, _gameOverBackgroundColor);
            
            // Pause text
            string pauseText = "PAUSED";
            Vector2 pauseSize = MeasureString(pauseText);
            Vector2 pausePos = new Vector2(
                (Game1.SCREEN_WIDTH - pauseSize.X) / 2,
                Game1.SCREEN_HEIGHT / 2 - 50
            );
            DrawTextWithShadow(spriteBatch, pauseText, pausePos, Color.Yellow, 2f);
            
            // Resume instruction
            string resumeText = "Press P to Resume";
            Vector2 resumeSize = MeasureString(resumeText);
            Vector2 resumePos = new Vector2(
                (Game1.SCREEN_WIDTH - resumeSize.X) / 2,
                pausePos.Y + 60
            );
            DrawTextWithShadow(spriteBatch, resumeText, resumePos, Color.White);
        }
        
        private void DrawTextWithShadow(SpriteBatch spriteBatch, string text, Vector2 position, Color color, float scale = 1f)
        {
            if (_font == null)
            {
                // Fallback: draw simple colored rectangles as text placeholder
                Rectangle textRect = new Rectangle((int)position.X, (int)position.Y, text.Length * 10, 20);
                spriteBatch.Draw(_pixelTexture, textRect, color * 0.5f);
                return;
            }
            
            // Draw shadow
            spriteBatch.DrawString(_font, text, position + new Vector2(SHADOW_OFFSET), _shadowColor, 0f, Vector2.Zero, scale, SpriteEffects.None, 0f);
            
            // Draw main text
            spriteBatch.DrawString(_font, text, position, color, 0f, Vector2.Zero, scale, SpriteEffects.None, 0f);
        }
        
        private Vector2 MeasureString(string text)
        {
            if (_font == null)
                return new Vector2(text.Length * 10, 20); // Fallback measurement
                
            return _font.MeasureString(text);
        }
    }
}
