using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using System;

namespace SubwaySurfers2D
{
    public class Coin
    {
        private Texture2D _texture;
        private Vector2 _position;
        private Vector2 _velocity;
        private float _rotation;
        private float _rotationSpeed;
        private float _scale;
        private float _scaleDirection;
        
        // Movement and animation settings
        private const float MOVE_SPEED = 200f;
        private const float ROTATION_SPEED = 3f;
        private const float SCALE_SPEED = 1f;
        private const float MIN_SCALE = 0.8f;
        private const float MAX_SCALE = 1.2f;
        
        public Vector2 Position => _position;
        public int Value { get; private set; }
        
        public Coin(Texture2D texture, Vector2 startPosition, int value = 10)
        {
            _texture = texture;
            _position = startPosition;
            _velocity = new Vector2(0, MOVE_SPEED);
            Value = value;
            
            // Initialize animation properties
            _rotation = 0f;
            _rotationSpeed = ROTATION_SPEED;
            _scale = 1f;
            _scaleDirection = 1f;
            
            // Add some randomness to animation
            Random random = new Random();
            _rotationSpeed += (float)(random.NextDouble() - 0.5) * 2f;
        }
        
        public void Update(GameTime gameTime)
        {
            float deltaTime = (float)gameTime.ElapsedGameTime.TotalSeconds;
            
            // Move coin down the screen
            _position += _velocity * deltaTime;
            
            // Update rotation animation
            _rotation += _rotationSpeed * deltaTime;
            if (_rotation > MathHelper.TwoPi)
                _rotation -= MathHelper.TwoPi;
            
            // Update scale animation (pulsing effect)
            _scale += _scaleDirection * SCALE_SPEED * deltaTime;
            if (_scale >= MAX_SCALE)
            {
                _scale = MAX_SCALE;
                _scaleDirection = -1f;
            }
            else if (_scale <= MIN_SCALE)
            {
                _scale = MIN_SCALE;
                _scaleDirection = 1f;
            }
        }
        
        public void Draw(SpriteBatch spriteBatch)
        {
            // Calculate center point for rotation
            Vector2 origin = new Vector2(_texture.Width / 2f, _texture.Height / 2f);
            Vector2 drawPosition = _position + origin;
            
            spriteBatch.Draw(
                _texture,
                drawPosition,
                null,
                Color.Gold,
                _rotation,
                origin,
                _scale,
                SpriteEffects.None,
                0f
            );
        }
        
        public Rectangle GetBounds()
        {
            // Adjust bounds based on current scale
            int scaledWidth = (int)(_texture.Width * _scale);
            int scaledHeight = (int)(_texture.Height * _scale);
            
            return new Rectangle(
                (int)(_position.X + (_texture.Width - scaledWidth) / 2),
                (int)(_position.Y + (_texture.Height - scaledHeight) / 2),
                scaledWidth,
                scaledHeight
            );
        }
        
        // إنشاء عملة في مسار محدد
        public static Coin CreateCoin(Texture2D texture, int lane, int value = 10)
        {
            // تحديد موقع المسار
            float x = (lane * Game1.LANE_WIDTH) + (Game1.LANE_WIDTH / 2) - (texture.Width / 2);
            Vector2 position = new Vector2(x, -texture.Height);
            
            return new Coin(texture, position, value);
        }
        
        // إنشاء مجموعة من العملات في خط مستقيم
        public static Coin[] CreateCoinLine(Texture2D texture, int lane, int count, float spacing = 50f, int value = 10)
        {
            Coin[] coins = new Coin[count];
            
            for (int i = 0; i < count; i++)
            {
                float x = (lane * Game1.LANE_WIDTH) + (Game1.LANE_WIDTH / 2) - (texture.Width / 2);
                float y = -texture.Height - (i * spacing);
                Vector2 position = new Vector2(x, y);
                
                coins[i] = new Coin(texture, position, value);
            }
            
            return coins;
        }
        
        // إنشاء عملات في جميع المسارات (نمط متقاطع)
        public static Coin[] CreateCoinPattern(Texture2D texture, int value = 10)
        {
            Coin[] coins = new Coin[3]; // عملة في كل مسار
            
            for (int lane = 0; lane < 3; lane++)
            {
                float x = (lane * Game1.LANE_WIDTH) + (Game1.LANE_WIDTH / 2) - (texture.Width / 2);
                float y = -texture.Height - (lane * 30f); // تباعد قطري
                Vector2 position = new Vector2(x, y);
                
                coins[lane] = new Coin(texture, position, value);
            }
            
            return coins;
        }
    }
}
