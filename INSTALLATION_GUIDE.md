# دليل التثبيت والتشغيل - لعبة Subway Surfers 2D

## الخطوة 1: تثبيت المتطلبات الأساسية

### تثبيت Visual Studio
1. قم بتحميل Visual Studio Community (مجاني) من:
   https://visualstudio.microsoft.com/vs/community/
2. أثناء التثبيت، تأكد من اختيار:
   - **.NET desktop development workload**
   - **Game development with Unity** (اختياري)

### تثبيت .NET SDK
1. اذهب إلى: https://dotnet.microsoft.com/download
2. حمل وثبت **.NET 6.0 SDK** أو أحدث
3. أعد تشغيل الكمبيوتر بعد التثبيت

## الخطوة 2: تحضير المشروع

### فتح المشروع في Visual Studio
1. افتح Visual Studio
2. اختر **"Open a project or solution"**
3. انتقل إلى مجلد المشروع واختر `SubwaySurfers2D.csproj`
4. انتظر حتى يتم تحميل المشروع وتحميل الحزم

### التحقق من تثبيت MonoGame
إذا ظهرت أخطاء متعلقة بـ MonoGame:
1. افتح **Package Manager Console** في Visual Studio
2. اكتب الأمر التالي:
```
Install-Package MonoGame.Framework.DesktopGL
Install-Package MonoGame.Content.Builder.Task
```

## الخطوة 3: تشغيل اللعبة

### الطريقة الأولى: من Visual Studio
1. تأكد من أن المشروع محدد كـ **Startup Project**
2. اختر **Release** أو **Debug** من القائمة العلوية
3. اضغط **F5** أو اختر **Debug > Start Debugging**

### الطريقة الثانية: من Command Line
1. افتح **Command Prompt** أو **PowerShell**
2. انتقل إلى مجلد المشروع:
```cmd
cd "C:\path\to\your\project"
```
3. شغل الأمر:
```cmd
dotnet run
```

### الطريقة الثالثة: استخدام ملف Batch
1. انقر نقراً مزدوجاً على `build_and_run.bat`
2. سيقوم الملف بالبناء والتشغيل تلقائياً

## استكشاف الأخطاء الشائعة

### خطأ: "dotnet command not found"
**الحل:**
1. تأكد من تثبيت .NET SDK
2. أعد تشغيل Command Prompt
3. تحقق من متغيرات البيئة (PATH)

### خطأ: "MonoGame.Framework not found"
**الحل:**
1. افتح Package Manager Console في Visual Studio
2. شغل: `Update-Package -reinstall`
3. أو احذف مجلد `bin` و `obj` وأعد البناء

### خطأ: "Content Pipeline"
**الحل:**
1. تأكد من وجود ملف `Content.mgcb`
2. انقر بالزر الأيمن على المشروع > **Rebuild**

### اللعبة لا تعمل أو بطيئة
**الحل:**
1. تأكد من تشغيلها في وضع **Release**
2. تحقق من تعريفات كرت الرسوميات
3. أغلق البرامج الأخرى المستهلكة للذاكرة

## إضافة المحتوى (الصور والأصوات)

### إضافة الصور
1. ضع ملفات PNG في مجلد `Content`
2. انقر بالزر الأيمن على المشروع > **Add > Existing Item**
3. اختر الصور وتأكد من أن **Build Action** مضبوط على **Content**

### إضافة الأصوات
1. أنشئ مجلد `Content/Sounds`
2. ضع ملفات WAV أو MP3
3. أضفها للمشروع كما في الصور
4. حدث `AudioManager.cs` لتحميل الأصوات

## نصائح للتطوير

### استخدام Git
```cmd
git init
git add .
git commit -m "Initial commit"
```

### تحسين الأداء
- استخدم **Release mode** للاختبار النهائي
- راقب استهلاك الذاكرة في **Task Manager**
- استخدم **Visual Studio Diagnostic Tools**

### إضافة ميزات جديدة
1. أنشئ branch جديد:
```cmd
git checkout -b new-feature
```
2. اكتب الكود مع التعليقات
3. اختبر الميزة جيداً
4. ادمج التغييرات:
```cmd
git checkout main
git merge new-feature
```

## الدعم والمساعدة

### مصادر مفيدة
- **MonoGame Documentation**: https://docs.monogame.net/
- **C# Documentation**: https://docs.microsoft.com/en-us/dotnet/csharp/
- **Visual Studio Help**: https://docs.microsoft.com/en-us/visualstudio/

### حل المشاكل
1. تحقق من **Output Window** في Visual Studio
2. راجع **Error List** للأخطاء التفصيلية
3. استخدم **Debugger** لتتبع المشاكل

### تحديث المشروع
```cmd
dotnet restore
dotnet build
```

## ملاحظات مهمة

- تأكد من أن جميع الملفات في نفس المجلد
- لا تحذف ملف `.csproj` أو مجلد `Content`
- احتفظ بنسخة احتياطية قبل إجراء تغييرات كبيرة
- اختبر اللعبة بعد كل تعديل

## إنشاء ملف تنفيذي

لإنشاء ملف `.exe` قابل للتوزيع:
```cmd
dotnet publish -c Release -r win-x64 --self-contained true
```

سيتم إنشاء الملفات في مجلد `bin/Release/net6.0/win-x64/publish/`
